/**
 * 验证码识别模块
 * 提供验证码识别、高亮显示、复制等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2025-01-28
 */

(function(global) {
    'use strict';

    /**
     * 验证码识别配置
     */
    const VerificationCodeConfig = {
        // 验证码长度范围
        MIN_LENGTH: 4,
        MAX_LENGTH: 8,
        
        // 优先级定义
        PRIORITY: {
            ULTRA_HIGH: 'ultra-high',
            HIGH: 'high',
            MEDIUM: 'medium',
            LOW: 'low'
        },
        
        // CSS类名
        CSS_CLASSES: {
            HIGHLIGHTED: 'highlighted-code',
            COPIED: 'copied'
        }
    };

    /**
     * 验证码识别模式定义
     */
    const VerificationPatterns = {
        // 超高优先级模式 - 针对特定格式的精确匹配
        ultraHighPriority: [
            // 专门针对 "您的验证码为: e9b1e1" 这种格式
            /您的验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
            /您的验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
            /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
            /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
            
            // 其他常见的精确格式
            /your code is[：:\s]*([A-Za-z0-9]{4,8})/i,
            /the code is[：:\s]*([A-Za-z0-9]{4,8})/i,
            /verification code is[：:\s]*([A-Za-z0-9]{4,8})/i,
        ],

        // 高优先级模式 - 精确匹配（支持大小写字母和数字）
        highPriority: [
            // 中文验证码模式 - 精确匹配，支持各种连接词
            /验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /您的验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /动态验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /邮箱验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /短信验证码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            
            // 英文验证码模式 - 精确匹配
            /verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
            /your verification code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
            /code[：:\s]*is\s*([A-Za-z0-9]{4,8})/i,
            /email verification code[：:\s]*[is]?\s*([A-Za-z0-9]{4,8})/i,
            
            // OTP模式
            /OTP[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /one.time.password[：:\s]*([A-Za-z0-9]{4,8})/i,
            
            // 安全码模式
            /安全码[：:\s]*[是为]?\s*([A-Za-z0-9]{4,8})/i,
            /security code[：:\s]*([A-Za-z0-9]{4,8})/i,
        ],

        // 中等优先级模式 - 通用匹配
        mediumPriority: [
            // 通用验证码模式 - 支持更多连接词和格式
            /验证码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
            /verification code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
            /code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
            
            // 特殊格式的验证码模式
            /验证码为[：:\s]*([A-Za-z0-9]{4,8})/i,
            /验证码是[：:\s]*([A-Za-z0-9]{4,8})/i,
            /code is[：:\s]*([A-Za-z0-9]{4,8})/i,
            
            // PIN码模式
            /PIN[：:\s]*码?\s*[为是]?\s*([A-Za-z0-9]{4,8})/i,
            /pin[：:\s]*code?\s*[:]?\s*([A-Za-z0-9]{4,8})/i,
            
            // Token模式
            /token[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
            /令牌[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
            
            // 动态码模式
            /动态码[：:\s]*[为是]?\s*([A-Za-z0-9]{4,8})/i,
            /dynamic code[：:\s]*[:]?\s*([A-Za-z0-9]{4,8})/i,
        ],

        // 低优先级模式 - 数字匹配
        lowPriority: [
            // 6位数字（最常见）
            /\b(\d{6})\b/g,
            // 4-8位数字
            /\b(\d{4,8})\b/g,
        ]
    };

    /**
     * 验证码有效性检查
     * @param {string} code - 验证码
     * @param {string} priority - 优先级
     * @returns {boolean} 是否有效
     */
    function isValidVerificationCode(code, priority) {
        const trimmedCode = code.trim();

        // 长度检查
        if (trimmedCode.length < VerificationCodeConfig.MIN_LENGTH || 
            trimmedCode.length > VerificationCodeConfig.MAX_LENGTH) {
            return false;
        }

        // 超高优先级：允许所有格式，最宽松的验证
        if (priority === VerificationCodeConfig.PRIORITY.ULTRA_HIGH) {
            return true;
        }

        // 高优先级：允许所有格式
        if (priority === VerificationCodeConfig.PRIORITY.HIGH) {
            return true;
        }

        // 中等优先级：排除纯字母（除非是特定格式）
        if (priority === VerificationCodeConfig.PRIORITY.MEDIUM) {
            // 允许数字、数字+字母组合
            return /\d/.test(trimmedCode);
        }

        // 低优先级：仅数字，且排除明显不是验证码的数字
        if (priority === VerificationCodeConfig.PRIORITY.LOW) {
            // 排除年份、日期等
            const num = parseInt(trimmedCode);
            if (num < 1000 || num > 99999999) return false;
            if (trimmedCode.match(/^(19|20)\d{2}$/)) return false; // 年份
            if (trimmedCode.match(/^(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/)) return false; // 日期
            return true;
        }

        return false;
    }

    /**
     * 格式化验证码 - 保持原始大小写格式
     * @param {string} code - 原始验证码
     * @returns {string} 格式化后的验证码
     */
    function formatVerificationCode(code) {
        // 只进行去除首尾空格，保持原始大小写格式
        return code.trim();
    }

    /**
     * 验证码识别核心函数
     * @param {string} content - 邮件内容
     * @returns {Object} 识别结果 {code: string|null, priority: string|null}
     */
    function detectVerificationCode(content) {
        // 移除HTML标签，获取纯文本
        let textContent = content.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');

        // 按优先级顺序检查
        const allPatterns = [
            { patterns: VerificationPatterns.ultraHighPriority, priority: VerificationCodeConfig.PRIORITY.ULTRA_HIGH },
            { patterns: VerificationPatterns.highPriority, priority: VerificationCodeConfig.PRIORITY.HIGH },
            { patterns: VerificationPatterns.mediumPriority, priority: VerificationCodeConfig.PRIORITY.MEDIUM },
            { patterns: VerificationPatterns.lowPriority, priority: VerificationCodeConfig.PRIORITY.LOW }
        ];

        for (const group of allPatterns) {
            for (const pattern of group.patterns) {
                const matches = textContent.match(pattern);
                if (matches) {
                    // 对于全局匹配，取第一个匹配项
                    const code = matches[1] || matches[0];
                    if (code && isValidVerificationCode(code, group.priority)) {
                        return {
                            code: formatVerificationCode(code),
                            priority: group.priority
                        };
                    }
                }
            }
        }

        return { code: null, priority: null };
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    function generateUniqueId() {
        return 'code-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 转义正则表达式特殊字符
     * @param {string} string - 需要转义的字符串
     * @returns {string} 转义后的字符串
     */
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 增强的验证码识别（用于单个邮件显示）
     * @param {string} content - 邮件内容
     * @returns {Object} 识别结果 {code: string|null, content: string}
     */
    function detectAndHighlightVerificationCode(content) {
        const result = detectVerificationCode(content);
        const code = result ? result.code : null; // 安全地提取验证码

        if (code) {
            // 在内容中高亮显示验证码，添加唯一ID和点击属性
            const uniqueId = generateUniqueId();

            // 使用更精确的替换策略，避免重复替换
            // 先检查是否已经被高亮过了
            if (content.includes(VerificationCodeConfig.CSS_CLASSES.HIGHLIGHTED)) {
                // 如果已经包含高亮标签，直接返回原内容
                return { code, content };
            }

            // 创建一个安全的正则表达式，避免特殊字符问题
            const escapedCode = escapeRegExp(code);
            const highlightedContent = content.replace(
                new RegExp(`\\b${escapedCode}\\b`, 'g'), // 移除 'i' 标志以保持大小写
                `<span class="${VerificationCodeConfig.CSS_CLASSES.HIGHLIGHTED}" id="${uniqueId}" data-code="${code}" onclick="VerificationCodeModule.copyVerificationCode('${code}', '${uniqueId}')" title="点击复制验证码">${code}</span>`
            );
            return { code, content: highlightedContent };
        }
        return { code: null, content };
    }

    /**
     * 显示通知消息（需要外部实现）
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    function showNotification(message, type) {
        // 检查是否有外部的 showNotification 函数
        if (typeof global.showNotification === 'function') {
            global.showNotification(message, type);
        } else {
            // 简单的备用实现
            console.log(`[${type.toUpperCase()}] ${message}`);
            alert(message);
        }
    }

    /**
     * 备用复制方法（兼容旧浏览器）
     * @param {string} text - 要复制的文本
     * @returns {boolean} 是否复制成功
     */
    function fallbackCopyToClipboard(text) {
        try {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            // 选择并复制
            textArea.focus();
            textArea.select();
            const successful = document.execCommand('copy');

            // 清理
            document.body.removeChild(textArea);

            return successful;
        } catch (err) {
            console.error('备用复制方法也失败了:', err);
            return false;
        }
    }

    /**
     * 复制验证码功能
     * @param {string} code - 验证码
     * @param {string} elementId - 元素ID
     */
    function copyVerificationCode(code, elementId) {
        // 防止事件冒泡
        if (event) {
            event.stopPropagation();
        }

        // 获取目标元素
        const element = document.getElementById(elementId);

        // 复制到剪贴板
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(code).then(() => {
                // 复制成功
                showNotification(`验证码 ${code} 已复制到剪贴板`, 'success');
                handleCopySuccess(element, code);
            }).catch((error) => {
                // 复制失败，尝试使用备用方法
                console.error('复制失败，尝试备用方法:', error);
                handleFallbackCopy(code, element);
            });
        } else {
            // 浏览器不支持 navigator.clipboard，直接使用备用方法
            handleFallbackCopy(code, element);
        }
    }

    /**
     * 处理复制成功的视觉反馈
     * @param {HTMLElement} element - 目标元素
     * @param {string} code - 验证码
     */
    function handleCopySuccess(element, code) {
        if (element) {
            element.classList.add(VerificationCodeConfig.CSS_CLASSES.COPIED);

            // 临时改变文本内容
            const originalText = element.textContent;
            element.textContent = '已复制!';

            // 1秒后恢复原样
            setTimeout(() => {
                element.classList.remove(VerificationCodeConfig.CSS_CLASSES.COPIED);
                element.textContent = originalText;
            }, 1000);
        }
    }

    /**
     * 处理备用复制方法
     * @param {string} code - 验证码
     * @param {HTMLElement} element - 目标元素
     */
    function handleFallbackCopy(code, element) {
        if (fallbackCopyToClipboard(code)) {
            showNotification(`验证码 ${code} 已复制到剪贴板`, 'success');
            handleCopySuccess(element, code);
        } else {
            showNotification('复制失败，请手动复制验证码', 'error');
        }
    }

    /**
     * 为动态添加的验证码元素绑定点击事件（事件委托）
     * @param {string} containerId - 容器元素ID，默认为 'mailViewer'
     */
    function setupVerificationCodeClickHandlers(containerId = 'mailViewer') {
        // 使用事件委托，监听整个邮件查看区域的点击事件
        const container = document.getElementById(containerId);
        if (container) {
            // 移除之前的监听器（如果存在）
            container.removeEventListener('click', handleVerificationCodeClick);
            // 添加新的监听器
            container.addEventListener('click', handleVerificationCodeClick);
        }
    }

    /**
     * 处理验证码点击事件
     * @param {Event} event - 点击事件
     */
    function handleVerificationCodeClick(event) {
        // 检查点击的是否是验证码元素
        if (event.target.classList.contains(VerificationCodeConfig.CSS_CLASSES.HIGHLIGHTED)) {
            const code = event.target.getAttribute('data-code');
            const elementId = event.target.id;
            if (code && elementId) {
                copyVerificationCode(code, elementId);
            }
        }
    }

    /**
     * 验证码模块公共API
     */
    const VerificationCodeModule = {
        // 核心功能
        detectVerificationCode,
        detectAndHighlightVerificationCode,
        isValidVerificationCode,
        formatVerificationCode,

        // 复制功能
        copyVerificationCode,
        fallbackCopyToClipboard,

        // 事件处理
        setupVerificationCodeClickHandlers,

        // 工具函数
        generateUniqueId,
        escapeRegExp,

        // 配置
        config: VerificationCodeConfig,
        patterns: VerificationPatterns,

        // 版本信息
        version: '1.0.0'
    };

    // 导出模块
    if (typeof module !== 'undefined' && module.exports) {
        // Node.js 环境
        module.exports = VerificationCodeModule;
    } else if (typeof define === 'function' && define.amd) {
        // AMD 环境
        define([], function() {
            return VerificationCodeModule;
        });
    } else {
        // 浏览器环境
        global.VerificationCodeModule = VerificationCodeModule;
    }

})(typeof window !== 'undefined' ? window : this);
